# OpenOCD配置文件：DAPLink + MSPM0G3507
# 使用方法: openocd -f cmake/daplink_mspm0g3507.cfg

set FIRMWARE_PATH build/bin/mspm0_timer_test2.bin

# CMSIS-DAP接口配置 (DAPLink)
source [find interface/cmsis-dap.cfg]

# MSPM0目标配置
source [find target/ti_mspm0.cfg]




# 芯片特定配置
set CHIPNAME mspm0g3507
set WORKAREABASE 0x20000000
set WORKAREASIZE 0x8000

program $FIRMWARE_PATH  verify reset exit




# 打印连接信息
echo "DAPLink + MSPM0G3507 配置加载完成"
echo "使用命令："
echo "  reset halt    - 复位并停止"
echo "  flash write_image erase <file> - 烧录程序"
echo "  reset run     - 复位并运行"