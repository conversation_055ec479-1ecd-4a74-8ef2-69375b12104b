{"_from": "crc-32", "_id": "crc-32@1.2.2", "_inBundle": false, "_integrity": "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==", "_location": "/crc-32", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "crc-32", "name": "crc-32", "escapedName": "crc-32", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/crc-32/-/crc-32-1.2.2.tgz", "_shasum": "3cad35a934b8bf71f25ca524b6da51fb7eace2ff", "_spec": "crc-32", "_where": "/Users/<USER>/sect-crc-32", "author": {"name": "sheetjs"}, "bin": {"crc32": "bin/crc32.njs"}, "bugs": {"url": "https://github.com/SheetJS/js-crc32/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "crc32.js"}}, "dependencies": {}, "deprecated": false, "description": "Pure-JS CRC-32", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "codepage": "~1.10.0", "dtslint": "^0.1.2", "exit-on-epipe": "~1.0.1", "mocha": "~2.5.3", "printj": "~1.3.1", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "files": ["crc32.js", "crc32c.js", "bin/crc32.njs", "LICENSE", "README.md", "types/index.d.ts", "types/*.json"], "homepage": "https://sheetjs.com/", "keywords": ["crc", "crc32", "checksum"], "license": "Apache-2.0", "main": "crc32.js", "name": "crc-32", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-crc32.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "test": "make test"}, "types": "types/index.d.ts", "typesVersions": {"*": {"*": ["types/index.d.ts"]}}, "version": "1.2.2"}