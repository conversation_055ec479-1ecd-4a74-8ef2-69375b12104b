#include "User.h"

volatile uint32_t uwTick;

int main(void)
{
    SYSCFG_DL_init();
    //清除定时器中断标志
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);
    //使能定时器中断
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);

    while (1)
    {
        // 主循环 - 定时器中断每1ms触发一次，uwTick递增
        // 可以在此处添加其他应用逻辑
    }
}

//定时器的中断服务函数 已配置为1毫秒的周期
void TIMG0_IRQHandler(void)
{
    //检查并清除定时器中断标志
    switch( DL_TimerG_getPendingInterrupt(TIMER_0_INST) )
    {
        case DL_TIMER_IIDX_ZERO://如果是0溢出中断
            uwTick++;
            //清除中断标志
            DL_TimerG_clearInterruptStatus(TIMER_0_INST, DL_TIMERG_INTERRUPT_ZERO_EVENT);
            break;
        default:
            break;
    }
}