#include "User.h"

volatile uint32_t uwTick;

int main(void)
{
    // 在初始化之前先禁用所有中断
    __disable_irq();

    SYSCFG_DL_init();

    // 明确禁用TIMG8定时器，防止意外中断
    DL_TimerG_reset(TIMG8);
    DL_TimerG_disableInterrupt(TIMG8, DL_TIMERG_INTERRUPT_ZERO_EVENT);
    NVIC_DisableIRQ(TIMG8_INT_IRQn);
    NVIC_ClearPendingIRQ(TIMG8_INT_IRQn);

    // 清除所有待处理的中断
    NVIC_ClearPendingIRQ(TIMER_0_INST_INT_IRQN);

    // 设置中断优先级
    NVIC_SetPriority(TIMER_0_INST_INT_IRQN, 1);

    // 使能定时器中断
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);

    // 重新使能全局中断
    __enable_irq();

    while (1)
    {
        // 主循环 - 定时器中断每1ms触发一次，uwTick递增
        // 添加一个简单的LED闪烁来验证程序运行
        if (uwTick % 1000 == 0) {
            DL_GPIO_togglePins(GPIO_GRP_0_PORT, GPIO_GRP_0_PIN_0_PIN);
        }
    }
}

//定时器的中断服务函数 已配置为1毫秒的周期
void TIMG0_IRQHandler(void)
{
    //检查并清除定时器中断标志
    switch( DL_TimerG_getPendingInterrupt(TIMER_0_INST) )
    {
        case DL_TIMER_IIDX_ZERO://如果是0溢出中断
            uwTick++;
            //清除中断标志
            DL_TimerG_clearInterruptStatus(TIMER_0_INST, DL_TIMERG_INTERRUPT_ZERO_EVENT);
            break;
        default:
            break;
    }
}

//TIMG8中断处理程序 - 处理意外的TIMG8中断
void TIMG8_IRQHandler(void)
{
    //清除TIMG8的所有中断标志
    switch( DL_TimerG_getPendingInterrupt(TIMG8) )
    {
        case DL_TIMER_IIDX_ZERO:
            //清除中断标志
            DL_TimerG_clearInterruptStatus(TIMG8, DL_TIMERG_INTERRUPT_ZERO_EVENT);
            break;
        default:
            //清除所有可能的中断标志
            DL_TimerG_clearInterruptStatus(TIMG8, DL_TIMERG_INTERRUPT_ZERO_EVENT);
            break;
    }

    //禁用TIMG8中断，防止再次触发
    NVIC_DisableIRQ(TIMG8_INT_IRQn);
    DL_TimerG_disableInterrupt(TIMG8, DL_TIMERG_INTERRUPT_ZERO_EVENT);
}