# TIMG0中断向量修复报告

## 问题描述
项目在调试时立即进入默认中断处理程序，怀疑是TIMG0_IRQHandler中断向量位置错误。

## 根本原因分析

### 1. 中断号定义分析
根据MSPM0G350X设备头文件 (`mspm0g350x.h`)：
```c
typedef enum IRQn {
    // ...
    TIMG0_INT_IRQn = 16,     /* 32 TIMG0_INT Interrupt */
    // ...
} IRQn_Type;
```

### 2. 中断向量表位置计算
- TIMG0_INT_IRQn = 16
- 中断向量表位置 = 16 + 16 = 32 (0x80偏移)
- 这意味着TIMG0_IRQHandler应该位于中断向量表的第32个位置

### 3. 原始问题
启动文件中的中断向量表排列不正确，没有按照官方IRQn_Type枚举的顺序排列。

## 修复措施

### 1. 修正中断向量表 (`cmake/startup_mspm0g3507.c`)
```c
/* 修正前 - 错误的向量表排列 */
// 中断向量表位置不匹配IRQn枚举

/* 修正后 - 基于MSPM0G350X官方定义 */
const uint32_t g_pfnVectors[] = {
    // 系统中断 (0-15)
    // ...
    
    // MSPM0G350X 外设中断 (16-47) - 基于mspm0g350x.h IRQn_Type枚举
    (uint32_t)GROUP0_IRQHandler,        /* 16: IRQ0 */
    (uint32_t)GROUP1_IRQHandler,        /* 17: IRQ1 */
    // ...
    (uint32_t)TIMG0_IRQHandler,         /* 32: IRQ16 - 正确位置 */
    // ...
};
```

### 2. 完善中断处理函数 (`code/User/Src/main.c`)
```c
void TIMG0_IRQHandler(void)
{
    switch( DL_TimerG_getPendingInterrupt(TIMER_0_INST) )
    {
        case DL_TIMER_IIDX_ZERO:
            uwTick++;
            // 添加中断标志清除
            DL_TimerG_clearInterruptStatus(TIMER_0_INST, DL_TIMERG_INTERRUPT_ZERO_EVENT);
            break;
        default:
            break;
    }
}
```

## 验证结果

### 1. 编译验证
```bash
Memory region         Used Size  Region Size  %age Used
           FLASH:       23996 B       128 KB     18.31%
            SRAM:        9016 B        32 KB     27.51%
```

### 2. 符号表验证
```bash
$ arm-none-eabi-nm bin/mspm0_timer_test2.elf | grep TIMG0_IRQHandler
00002fe0 T TIMG0_IRQHandler
```

### 3. 中断向量表验证
- 偏移0x80处的值：`e12f0000` (小端格式)
- 实际地址：`0x00002fe1` (Thumb模式，最低位为1)
- 对应符号：`TIMG0_IRQHandler` (0x00002fe0)

## 技术要点

### 1. ARM Cortex-M0+ 中断向量表
- 系统中断：0-15
- 外设中断：16开始
- 每个中断向量占4字节

### 2. MSPM0G350X 中断映射
- IRQn枚举值直接对应中断向量表位置
- TIMG0_INT_IRQn = 16 → 向量表位置32

### 3. Thumb模式地址
- 函数地址最低位设为1表示Thumb模式
- 实际执行地址为偶数地址

## 修复状态
✅ **已修复** - 中断向量表已正确对齐，TIMG0中断应能正常工作

## 测试建议
1. 使用调试器验证uwTick变量是否按1ms周期递增
2. 确认不再进入Default_Handler
3. 验证定时器中断的精确性

---
**修复完成时间**: 2024-12-19  
**修复人员**: Alex (Engineer)  
**验证状态**: 编译通过，符号表验证正确
