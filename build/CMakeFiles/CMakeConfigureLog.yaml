
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:200 (message)"
      - "CMakeLists.txt:26 (project)"
    message: |
      The target system is: Generic -  - arm
      The host system is: Linux - 6.8.0-60-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc 
      Build flags: -mcpu=cortex-m0plus;-mthumb;-Wall;-Wextra;-fdata-sections;-ffunction-sections;-Wno-unused-parameter;-Wno-unused-variable;-Wno-pedantic;-Wall;-Wextra;-Wshadow;-Wundef;-Wno-unused-parameter
      Id flags:  
      
      The output was:
      1
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x16): undefined reference to `_exit'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-closer.o): in function `_close_r':
      closer.c:(.text._close_r+0xc): undefined reference to `_close'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-lseekr.o): in function `_lseek_r':
      lseekr.c:(.text._lseek_r+0x10): undefined reference to `_lseek'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-readr.o): in function `_read_r':
      readr.c:(.text._read_r+0x10): undefined reference to `_read'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-writer.o): in function `_write_r':
      writer.c:(.text._write_r+0x10): undefined reference to `_write'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
      sbrkr.c:(.text._sbrk_r+0xc): undefined reference to `_sbrk'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc 
      Build flags: -mcpu=cortex-m0plus;-mthumb;-Wall;-Wextra;-fdata-sections;-ffunction-sections;-Wno-unused-parameter;-Wno-unused-variable;-Wno-pedantic;-Wall;-Wextra;-Wshadow;-Wundef;-Wno-unused-parameter
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/3.30.3/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++ 
      Build flags: -mcpu=cortex-m0plus;-mthumb;-Wall;-Wextra;-fdata-sections;-ffunction-sections;-Wno-unused-parameter;-Wno-unused-variable;-Wno-pedantic;-fno-rtti;-fno-exceptions;-fno-threadsafe-statics
      Id flags:  
      
      The output was:
      1
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x16): undefined reference to `_exit'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-closer.o): in function `_close_r':
      closer.c:(.text._close_r+0xc): undefined reference to `_close'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-lseekr.o): in function `_lseek_r':
      lseekr.c:(.text._lseek_r+0x10): undefined reference to `_lseek'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-readr.o): in function `_read_r':
      readr.c:(.text._read_r+0x10): undefined reference to `_read'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-writer.o): in function `_write_r':
      writer.c:(.text._write_r+0x10): undefined reference to `_write'
      /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
      sbrkr.c:(.text._sbrk_r+0xc): undefined reference to `_sbrk'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++ 
      Build flags: -mcpu=cortex-m0plus;-mthumb;-Wall;-Wextra;-fdata-sections;-ffunction-sections;-Wno-unused-parameter;-Wno-unused-variable;-Wno-pedantic;-fno-rtti;-fno-exceptions;-fno-threadsafe-statics
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/3.30.3/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-vXUtHs"
      binary: "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-vXUtHs"
    cmakeVariables:
      CMAKE_C_FLAGS: "-mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter"
      CMAKE_C_FLAGS_DEBUG: "-Og -g3 -ggdb -O0 -g3 -ggdb"
      CMAKE_EXE_LINKER_FLAGS: "-mcpu=cortex-m0plus -mthumb -T/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/mspm0g3507.ld -Wl,--gc-sections -Wl,--print-memory-usage -Wl,-Map=mspm0_timer_test2.map --specs=nano.specs -u _printf_float -Wl,--start-group -lc -lm -Wl,--end-group -Wl,--no-warn-rwx-segments"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-vXUtHs'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6eefa
        [1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc   -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter  -std=gnu11   -v -o CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj -c /usr/local/share/cmake-3.30/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc
        Target: arm-none-eabi
        Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)'
        Thread model: single
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-fdata-sections' '-ffunction-sections' '-Wno-unused-variable' '-Wno-pedantic' '-Wall' '-Wextra' '-Wshadow' '-Wundef' '-Wno-unused-parameter' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_6eefa.dir/'
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/cc1 -quiet -v -imultilib thumb/v6-m/nofp -iprefix /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/ -isysroot /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi -D__USES_INITFINI__ /usr/local/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_6eefa.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mcpu=cortex-m0plus -mthumb -mfloat-abi=soft -mlibarch=armv6s-m -march=armv6s-m -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -std=gnu11 -version -fdata-sections -ffunction-sections -o /tmp/ccAO40EG.s
        GNU C11 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) version 14.2.1 20241119 (arm-none-eabi)
        	compiled by GNU C version 9.2.1 20191120 (Red Hat 9.2.1-2), GMP version 6.2.1, MPFR version 3.1.6, MPC version 1.0.3, isl version isl-0.15-1-g835ea3a-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include"
        ignoring nonexistent directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/usr/local/include"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include-fixed"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include
        End of search list.
        Compiler executable checksum: e89038eedc6170daa7e4ecd67cd2deb0
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-fdata-sections' '-ffunction-sections' '-Wno-unused-variable' '-Wno-pedantic' '-Wall' '-Wextra' '-Wshadow' '-Wundef' '-Wno-unused-parameter' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_6eefa.dir/'
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/as -v -march=armv6s-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj /tmp/ccAO40EG.s
        GNU assembler version 2.43.1 (arm-none-eabi) using BFD version (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 2.43.1.20241119
        COMPILER_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-fdata-sections' '-ffunction-sections' '-Wno-unused-variable' '-Wno-pedantic' '-Wall' '-Wextra' '-Wshadow' '-Wundef' '-Wno-unused-parameter' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.'
        [2/2] : && /usr/local/bin/cmake -E rm -f libcmTC_6eefa.a && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ar qc libcmTC_6eefa.a  CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ranlib libcmTC_6eefa.a && :
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include-fixed]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include]
        implicit include dirs: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include-fixed;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(arm-none-eabi-g\\+\\+|;ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(arm-none-eabi-g\\+\\+|;ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-vXUtHs']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/ninja -v cmTC_6eefa]
        ignore line: [[1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc   -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter  -std=gnu11   -v -o CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj -c /usr/local/share/cmake-3.30/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)']
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) ]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-fdata-sections' '-ffunction-sections' '-Wno-unused-variable' '-Wno-pedantic' '-Wall' '-Wextra' '-Wshadow' '-Wundef' '-Wno-unused-parameter' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_6eefa.dir/']
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/cc1 -quiet -v -imultilib thumb/v6-m/nofp -iprefix /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/ -isysroot /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi -D__USES_INITFINI__ /usr/local/share/cmake-3.30/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_6eefa.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mcpu=cortex-m0plus -mthumb -mfloat-abi=soft -mlibarch=armv6s-m -march=armv6s-m -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -std=gnu11 -version -fdata-sections -ffunction-sections -o /tmp/ccAO40EG.s]
        ignore line: [GNU C11 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) version 14.2.1 20241119 (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 9.2.1 20191120 (Red Hat 9.2.1-2)  GMP version 6.2.1  MPFR version 3.1.6  MPC version 1.0.3  isl version isl-0.15-1-g835ea3a-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include"]
        ignore line: [ignoring nonexistent directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/usr/local/include"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include-fixed"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: e89038eedc6170daa7e4ecd67cd2deb0]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-fdata-sections' '-ffunction-sections' '-Wno-unused-variable' '-Wno-pedantic' '-Wall' '-Wextra' '-Wshadow' '-Wundef' '-Wno-unused-parameter' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_6eefa.dir/']
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/as -v -march=armv6s-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj /tmp/ccAO40EG.s]
        ignore line: [GNU assembler version 2.43.1 (arm-none-eabi) using BFD version (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 2.43.1.20241119]
        ignore line: [COMPILER_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-fdata-sections' '-ffunction-sections' '-Wno-unused-variable' '-Wno-pedantic' '-Wall' '-Wextra' '-Wshadow' '-Wundef' '-Wno-unused-parameter' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.']
        ignore line: [[2/2] : && /usr/local/bin/cmake -E rm -f libcmTC_6eefa.a && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ar qc libcmTC_6eefa.a  CMakeFiles/cmTC_6eefa.dir/CMakeCCompilerABI.c.obj && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ranlib libcmTC_6eefa.a && :]
        ignore line: []
        ignore line: []
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-jxFIn1"
      binary: "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-jxFIn1"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -fno-rtti -fno-exceptions -fno-threadsafe-statics"
      CMAKE_CXX_FLAGS_DEBUG: "-Og -g3 -ggdb"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "-mcpu=cortex-m0plus -mthumb -T/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/mspm0g3507.ld -Wl,--gc-sections -Wl,--print-memory-usage -Wl,-Map=mspm0_timer_test2.map --specs=nano.specs -u _printf_float -Wl,--start-group -lc -lm -Wl,--end-group -Wl,--no-warn-rwx-segments"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-jxFIn1'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_9dc42
        [1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++   -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -fno-rtti -fno-exceptions -fno-threadsafe-statics    -v -o CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj -c /usr/local/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++
        Target: arm-none-eabi
        Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)'
        Thread model: single
        Supported LTO compression algorithms: zlib zstd
        gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-Wall' '-Wextra' '-fdata-sections' '-ffunction-sections' '-Wno-unused-parameter' '-Wno-unused-variable' '-Wno-pedantic' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_9dc42.dir/'
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/cc1plus -quiet -v -imultilib thumb/v6-m/nofp -iprefix /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/ -isysroot /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi -D__USES_INITFINI__ /usr/local/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_9dc42.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mcpu=cortex-m0plus -mthumb -mfloat-abi=soft -mlibarch=armv6s-m -march=armv6s-m -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -version -fdata-sections -ffunction-sections -fno-rtti -fno-exceptions -fno-threadsafe-statics -o /tmp/ccp0rpcV.s
        GNU C++17 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) version 14.2.1 20241119 (arm-none-eabi)
        	compiled by GNU C version 9.2.1 20191120 (Red Hat 9.2.1-2), GMP version 6.2.1, MPFR version 3.1.6, MPC version 1.0.3, isl version isl-0.15-1-g835ea3a-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include"
        ignoring nonexistent directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/usr/local/include"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include-fixed"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include"
        ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include
        End of search list.
        Compiler executable checksum: 761a3593099dc796ffc611563edb6c2e
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-Wall' '-Wextra' '-fdata-sections' '-ffunction-sections' '-Wno-unused-parameter' '-Wno-unused-variable' '-Wno-pedantic' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_9dc42.dir/'
         /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/as -v -march=armv6s-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj /tmp/ccp0rpcV.s
        GNU assembler version 2.43.1 (arm-none-eabi) using BFD version (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 2.43.1.20241119
        COMPILER_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-Wall' '-Wextra' '-fdata-sections' '-ffunction-sections' '-Wno-unused-parameter' '-Wno-unused-variable' '-Wno-pedantic' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.'
        [2/2] : && /usr/local/bin/cmake -E rm -f libcmTC_9dc42.a && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ar qc libcmTC_9dc42.a  CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ranlib libcmTC_9dc42.a && :
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed]
          add: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include/c++/14.2.1]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include/c++/14.2.1/backward]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include-fixed]
        collapse include dir [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include] ==> [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include]
        implicit include dirs: [/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include/c++/14.2.1;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include/c++/14.2.1/backward;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/lib/gcc/arm-none-eabi/14.2.1/include-fixed;/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(arm-none-eabi-g\\+\\+|;ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(arm-none-eabi-g\\+\\+|;ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/CMakeFiles/CMakeScratch/TryCompile-jxFIn1']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/ninja -v cmTC_9dc42]
        ignore line: [[1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++   -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -fno-rtti -fno-exceptions -fno-threadsafe-statics    -v -o CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj -c /usr/local/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)']
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) ]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-Wall' '-Wextra' '-fdata-sections' '-ffunction-sections' '-Wno-unused-parameter' '-Wno-unused-variable' '-Wno-pedantic' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_9dc42.dir/']
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/cc1plus -quiet -v -imultilib thumb/v6-m/nofp -iprefix /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/ -isysroot /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi -D__USES_INITFINI__ /usr/local/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_9dc42.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mcpu=cortex-m0plus -mthumb -mfloat-abi=soft -mlibarch=armv6s-m -march=armv6s-m -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -version -fdata-sections -ffunction-sections -fno-rtti -fno-exceptions -fno-threadsafe-statics -o /tmp/ccp0rpcV.s]
        ignore line: [GNU C++17 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) version 14.2.1 20241119 (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 9.2.1 20191120 (Red Hat 9.2.1-2)  GMP version 6.2.1  MPFR version 3.1.6  MPC version 1.0.3  isl version isl-0.15-1-g835ea3a-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include"]
        ignore line: [ignoring nonexistent directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/usr/local/include"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include-fixed"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring duplicate directory "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi/thumb/v6-m/nofp]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed]
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 761a3593099dc796ffc611563edb6c2e]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-Wall' '-Wextra' '-fdata-sections' '-ffunction-sections' '-Wno-unused-parameter' '-Wno-unused-variable' '-Wno-pedantic' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_9dc42.dir/']
        ignore line: [ /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/as -v -march=armv6s-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj /tmp/ccp0rpcV.s]
        ignore line: [GNU assembler version 2.43.1 (arm-none-eabi) using BFD version (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 2.43.1.20241119]
        ignore line: [COMPILER_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../libexec/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/thumb/v6-m/nofp/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/:/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m0plus' '-mthumb' '-Wall' '-Wextra' '-fdata-sections' '-ffunction-sections' '-Wno-unused-parameter' '-Wno-unused-variable' '-Wno-pedantic' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mlibarch=armv6s-m' '-march=armv6s-m' '-dumpdir' 'CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.']
        ignore line: [[2/2] : && /usr/local/bin/cmake -E rm -f libcmTC_9dc42.a && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ar qc libcmTC_9dc42.a  CMakeFiles/cmTC_9dc42.dir/CMakeCXXCompilerABI.cpp.obj && /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-ranlib libcmTC_9dc42.a && :]
        ignore line: []
        ignore line: []
        linker tool for 'CXX': [1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Running the CXX compiler's linker: "[1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Running the CXX compiler's linker: "[1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Running the CXX compiler's linker: "[1/2] /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-g++" "--version"
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:1192 (message)"
      - "/usr/local/share/cmake-3.30/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "CMakeLists.txt:30 (enable_language)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      arm-none-eabi-gcc (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 14.2.1 20241119
      Copyright (C) 2024 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
...
