Archive member included to satisfy reference by file (symbol)

/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o (exit)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
                              (_printf_float)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (_printf_common)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o) (__stdio_exit_handler)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o) (_fwalk_sglue)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o) (__sread)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o (memset)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (_localeconv_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o) (_close_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o) (errno)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o) (_impure_ptr)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o) (_lseek_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o) (_read_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o) (_write_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o (__libc_init_array)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o) (__retarget_lock_init_recursive)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o) (memchr)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strlen.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (strlen)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (_dtoa_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o) (_free_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (malloc)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o) (_malloc_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o) (__malloc_lock)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (_Balloc)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o) (_fflush_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o) (__global_locale)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o) (_sbrk_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o) (strcmp)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (memcpy)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__assert_func)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o) (_calloc_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o) (__ascii_mbtowc)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o) (__ascii_wctomb)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-ctype_.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o) (_ctype_)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o) (fiprintf)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o) (abort)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o) (_vfprintf_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o) (__sfvwrite_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o) (__swbuf_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o) (__swsetup_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o) (memmove)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o) (raise)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o) (_kill_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o) (_realloc_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o) (__smakebuf_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o) (_fstat_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o) (_isatty_r)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o) (_malloc_usable_size_r)
lib/libti_mspm0_driverlib.a(dl_common.c.obj)
                              CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj (DL_Common_delayCycles)
lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
                              CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj (DL_Timer_setClockConfig)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_uqi.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__gnu_thumb1_case_uqi)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_shi.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o) (__gnu_thumb1_case_shi)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_uidiv)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (__aeabi_idiv)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_dvmd_tls.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o) (__aeabi_idiv0)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (__aeabi_dcmpeq)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_dadd)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_ddiv)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o) (__eqdf2)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o) (__gedf2)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o) (__ledf2)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_dmul)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_dsub)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o) (__aeabi_dcmpun)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_d2iz)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_i2d)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o) (__aeabi_ui2d)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_clzsi2.o)
                              /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o) (__clzsi2)
/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)
                              CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj (__errno)

Discarded input sections

 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .rodata.all_implied_fbits
                0x00000000       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .data.__dso_handle
                0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .text          0x00000000       0x80 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .ARM.extab     0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .ARM.exidx     0x00000000       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .debug_line_str
                0x00000000       0xcb /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .ARM.attributes
                0x00000000       0x1b /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
 .text.exit     0x00000000       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
 .debug_frame   0x00000000       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
 .ARM.attributes
                0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-exit.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock
                0x00000000       0x18 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock
                0x00000000       0x18 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.__sfp    0x00000000       0xa8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock_all
                0x00000000       0x1c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock_all
                0x00000000       0x1c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
 .text.__seofread
                0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .text.__localeconv_l
                0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .text.localeconv
                0x00000000        0x8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xd4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
 .text.__libc_init_array
                0x00000000       0x48 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
 .debug_frame   0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
 .ARM.attributes
                0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-init.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss.__lock___arc4random_mutex
                0x00000000        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss.__lock___dd_hash_mutex
                0x00000000        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss.__lock___tz_mutex
                0x00000000        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss.__lock___env_recursive_mutex
                0x00000000        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss.__lock___at_quick_exit_mutex
                0x00000000        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .bss.__lock___atexit_recursive_mutex
                0x00000000        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strlen.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strlen.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
 .text.free     0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text.__s2b    0x00000000       0x98 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text.__ulp    0x00000000       0x40 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text.__b2d    0x00000000       0x98 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text.__ratio  0x00000000       0x54 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text._mprec_log10
                0x00000000       0x34 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text.__copybits
                0x00000000       0x42 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text.__any_on
                0x00000000       0x46 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .rodata.__mprec_tinytens
                0x00000000       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
 .text.fflush   0x00000000       0x30 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .text._setlocale_r
                0x00000000       0x40 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .text.__locale_mb_cur_max
                0x00000000       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .text.setlocale
                0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .bss._PathLocale
                0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
 .text          0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
 .debug_line_str
                0x00000000       0xdd /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
 .debug_frame   0x00000000       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
 .ARM.attributes
                0x00000000       0x1c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strcmp.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .text.__assert
                0x00000000        0xa /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
 .text._mbtowc_r
                0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
 .text._wctomb_r
                0x00000000       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-ctype_.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-ctype_.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-ctype_.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
 .text._fprintf_r
                0x00000000       0x16 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .text.__sprint_r
                0x00000000       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .text.vfprintf
                0x00000000       0x18 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
 .text.__sfvwrite_r
                0x00000000      0x2c4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
 .debug_frame   0x00000000       0x30 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
 .ARM.attributes
                0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fvwrite.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
 .text.__swbuf  0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
 .text.memmove  0x00000000       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
 .debug_frame   0x00000000       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
 .ARM.attributes
                0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memmove.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text._init_signal_r
                0x00000000       0x2a /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text._signal_r
                0x00000000       0x32 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text.__sigtramp_r
                0x00000000       0x46 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text.signal   0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text._init_signal
                0x00000000       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text.__sigtramp
                0x00000000       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
 .text._realloc_r
                0x00000000       0x60 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
 .debug_frame   0x00000000       0x34 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
 .ARM.attributes
                0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reallocr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
 .text._malloc_usable_size_r
                0x00000000       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
 .debug_frame   0x00000000       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
 .ARM.attributes
                0x00000000       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-msizer.o)
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .data          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .bss           0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.I2C0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.SYSCTL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.WWDT1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.CPUSS  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.CANFD0
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.IOMUX  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.GPIOB  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.CRC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.WWDT0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.SPI1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.DAC0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.I2C1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.GPIOA  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.FLASHCTL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TRNG   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.RTC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.SPI0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.AES    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.DMA    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.OPA0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.OPA1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMA0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMA1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.UART3  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.UART0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.UART1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.UART2  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.COMP0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.COMP1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.COMP2  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.WUC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.FACTORYREGION
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.ADC0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.ADC0_PERIPHERALREGIONSVT
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.ADC1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.ADC1_PERIPHERALREGIONSVT
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMG0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.VREF   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.MATHACL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMG12
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMG6  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMG7  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.TIMG8  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .rodata.DEBUGSS
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .data          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .bss           0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.I2C0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.SYSCTL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.WWDT1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.CPUSS  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.CANFD0
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.IOMUX  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.GPIOB  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.CRC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.WWDT0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.SPI1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.DAC0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.I2C1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.GPIOA  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.FLASHCTL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TRNG   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.RTC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.SPI0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.AES    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.DMA    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.OPA0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.OPA1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMA0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMA1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.UART3  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.UART0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.UART1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.UART2  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.COMP0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.COMP1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.COMP2  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.WUC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.FACTORYREGION
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.ADC0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.ADC0_PERIPHERALREGIONSVT
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.ADC1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.ADC1_PERIPHERALREGIONSVT
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMG0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.VREF   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.MATHACL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMG12
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMG6  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMG7  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.TIMG8  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.DEBUGSS
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0xac0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x75 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x24 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x94 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x57 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x9e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x364 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x112 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x4a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x58 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x1e5 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x170 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x103 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x1df CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x86 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x125 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0xd1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x4da CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x11f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x2a9e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x7fa CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x8c0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x22d CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0xd04 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x226c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0xee9 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x5cc6 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x3636 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x24a8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x154 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x33f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x24ec CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x281 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x2cd8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0xea7 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x44c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x1617 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x19f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x46 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x3c7 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x3ac CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x265 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x47e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000     0x2ea1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0xf0e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x19b CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x320 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x2e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x7a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x164 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x1be CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0xcd CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x7de CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x17c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x52 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x235 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x57 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x56b CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x70 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x4e8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x6e1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x42b CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x2e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0xbd CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x747 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000      0x578 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x2e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0xbe CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00000000       0x3a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .text          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .data          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .bss           0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.I2C0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.SYSCTL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.WWDT1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.CPUSS  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.CANFD0
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.IOMUX  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.GPIOB  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.CRC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.WWDT0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.SPI1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.DAC0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.I2C1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.GPIOA  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.FLASHCTL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TRNG   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.RTC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.SPI0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.AES    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.DMA    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.OPA0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.OPA1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMA0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMA1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.UART3  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.UART0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.UART1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.UART2  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.COMP0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.COMP1  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.COMP2  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.WUC    0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.FACTORYREGION
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.ADC0   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.ADC0_PERIPHERALREGIONSVT
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.ADC1   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.ADC1_PERIPHERALREGIONSVT
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMG0  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.VREF   0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.MATHACL
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMG12
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMG6  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMG7  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.TIMG8  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .rodata.DEBUGSS
                0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0xac0 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x75 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x24 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x94 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x57 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x9e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x364 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x112 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x4a CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x58 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x1e5 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x170 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x103 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x1df CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x86 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x125 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0xd1 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x4da CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x11f CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x2a9e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x7fa CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x8c0 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x22d CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0xd04 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x226c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0xee9 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x5cc6 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x3636 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x24a8 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x154 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x33f CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x24ec CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x281 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x2cd8 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0xea7 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x44c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x1617 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x19f CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x46 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x3c7 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x3ac CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x265 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x47e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000     0x2ea1 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0xf0e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x19b CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x320 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x2e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x7a CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x164 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x1be CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0xcd CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x7de CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x17c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x52 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x235 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x57 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x56b CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x70 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x4e8 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x6e1 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x42b CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x2e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0xbd CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x747 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000      0x578 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x2e CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0xbe CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00000000       0x3a CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .data          0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .bss           0x00000000        0x0 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .bss.__env     0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .data.environ  0x00000000        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._execve  0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._fork    0x00000000       0x18 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._link    0x00000000       0x20 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._open    0x00000000       0x18 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._stat    0x00000000       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._times   0x00000000       0x14 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._unlink  0x00000000       0x1e CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .text._wait    0x00000000       0x1e CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000      0xac0 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x75 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x24 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x94 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x57 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000      0x364 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x4a CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x58 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000      0x1e5 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00000000      0x170 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .text          0x00000000        0x0 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .data          0x00000000        0x0 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .bss           0x00000000        0x0 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.I2C0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.SYSCTL
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.WWDT1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.CPUSS  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.CANFD0
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.IOMUX  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.GPIOB  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.CRC    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.WWDT0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.SPI1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.DAC0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.I2C1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.GPIOA  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.FLASHCTL
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TRNG   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.RTC    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.SPI0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.AES    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.DMA    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.OPA0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.OPA1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMA0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMA1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.UART3  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.UART0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.UART1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.UART2  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.COMP0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.COMP1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.COMP2  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.WUC    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.FACTORYREGION
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.ADC0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.ADC0_PERIPHERALREGIONSVT
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.ADC1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.ADC1_PERIPHERALREGIONSVT
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMG0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.VREF   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.MATHACL
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMG12
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMG6  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMG7  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.TIMG8  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .rodata.DEBUGSS
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x22 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x103 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x6a lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x1df lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x86 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x22 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x125 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x1c lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x22 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0xd1 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x4da lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x11f lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x2a9e lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x7fa lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x8c0 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x22d lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0xd04 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x226c lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0xee9 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x5cc6 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x3636 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x24a8 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x154 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x33f lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x24ec lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x281 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x2cd8 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0xea7 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x44c lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x1617 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x19f lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000       0x46 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x3c7 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x3ac lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x265 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0x47e lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000     0x2ea1 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00000000      0xf0e lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .group         0x00000000        0xc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text          0x00000000        0x0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .data          0x00000000        0x0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .bss           0x00000000        0x0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.I2C0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.SYSCTL
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.WWDT1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.CPUSS  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.CANFD0
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.IOMUX  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.GPIOB  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.CRC    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.WWDT0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.SPI1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.DAC0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.I2C1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.GPIOA  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.FLASHCTL
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TRNG   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.RTC    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.SPI0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.AES    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.DMA    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.OPA0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.OPA1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMA0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMA1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.UART3  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.UART0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.UART1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.UART2  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.COMP0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.COMP1  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.COMP2  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.WUC    0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.FACTORYREGION
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.ADC0   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.ADC0_PERIPHERALREGIONSVT
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.ADC1   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.ADC1_PERIPHERALREGIONSVT
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMG0  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.VREF   0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.MATHACL
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMG12
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMG6  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMG7  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.TIMG8  0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .rodata.DEBUGSS
                0x00000000        0x4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCCPDirection
                0x00000000       0x1c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getClockConfig
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_initCaptureMode
                0x00000000      0x16c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_initCaptureTriggerMode
                0x00000000       0xc4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_initCaptureCombinedMode
                0x00000000       0xd0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_initCompareMode
                0x00000000       0xd4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_initCompareTriggerMode
                0x00000000       0xb0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_initPWMMode
                0x00000000      0x130 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptureCompareValue
                0x00000000       0x34 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptureCompareCtl
                0x00000000       0x3c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setSecondCompSrcDn
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getSecondCompSrcDn
                0x00000000       0x3c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setSecondCompSrcUp
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getSecondCompSrcUp
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setSecondCompActionDn
                0x00000000       0x4c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getSecondCompActionDn
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setSecondCompActionUp
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getSecondCompActionUp
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_enableSuppressionOfCompEvent
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_disableSuppressionOfCompEvent
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCaptCompUpdateMethod
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptCompUpdateMethod
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCaptureCompareOutCtl
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptureCompareOutCtl
                0x00000000       0x34 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCaptureCompareAction
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptureCompareAction
                0x00000000       0x3c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_overrideCCPOut
                0x00000000       0x48 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCaptureCompareInput
                0x00000000       0x44 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptureCompareInput
                0x00000000       0x36 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCaptureCompareInputFilter
                0x00000000       0x46 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getCaptureCompareInputFilter
                0x00000000       0x38 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_enableCaptureCompareInputFilter
                0x00000000       0x3c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_disableCaptureCompareInputFilter
                0x00000000       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_isCaptureCompareInputFilterEnabled
                0x00000000       0x48 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_saveConfiguration
                0x00000000      0x1d8 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_restoreConfiguration
                0x00000000      0x1dc lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_TimerA_initPWMMode
                0x00000000       0xf4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setFaultSourceConfig
                0x00000000       0x44 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getFaultSourceConfig
                0x00000000       0x38 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_TimerA_saveConfiguration
                0x00000000      0x2c0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_TimerA_restoreConfiguration
                0x00000000      0x2b8 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getInChanConfig
                0x00000000       0xa0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_getInChanPairConfig
                0x00000000       0x52 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_configQEIHallInputMode
                0x00000000       0x4c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0xaba lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x22 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x22 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x8e lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x51 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x103 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x6a lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x1df lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x86 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x1c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x22 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0xd1 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x4da lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x11f lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x2a9e lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x7fa lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x8c0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x22d lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0xd04 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x226c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0xee9 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x5cc6 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x3636 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x24a8 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x154 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x33f lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x24ec lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x281 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x2cd8 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0xea7 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x44c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x1617 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x19f lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000       0x46 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x3c7 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x3ac lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x265 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x47e lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000     0x2ea1 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0xf0e lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x6e1 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00000000      0x42b lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_uqi.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_uqi.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_shi.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_shi.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_clzsi2.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_clzsi2.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
 .rodata.all_implied_fbits
                0x00000000       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
 .eh_frame      0x00000000        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
 .text          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o
 .data          0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o
 .bss           0x00000000        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x00000000         0x00020000         xr
SRAM             0x20000000         0x00008000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/crt0.o
START GROUP
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libm.a
END GROUP
LOAD CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
LOAD CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
LOAD CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
LOAD CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
LOAD lib/libti_mspm0_driverlib.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libm.a
START GROUP
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a
END GROUP
START GROUP
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a
END GROUP
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o
                0x00001000                        _stack_size = 0x1000
                0x00001000                        _heap_size = 0x1000
                0x20008000                        _estack = (ORIGIN (SRAM) + LENGTH (SRAM))

.isr_vector     0x00000000       0xc0
                0x00000000                        . = ALIGN (0x4)
 *(.isr_vector)
 .isr_vector    0x00000000       0xc0 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
                0x00000000                g_pfnVectors
                0x000000c0                        . = ALIGN (0x4)

.text           0x000000c0     0x5a8c
                0x000000c0                        . = ALIGN (0x4)
 *(.text)
 .text          0x000000c0        0xe /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strlen.o)
                0x000000c0                strlen
 *fill*         0x000000ce        0x2 
 .text          0x000000d0       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_uqi.o)
                0x000000d0                __gnu_thumb1_case_uqi
 .text          0x000000e4       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_shi.o)
                0x000000e4                __gnu_thumb1_case_shi
 .text          0x000000f8      0x114 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
                0x000000f8                __udivsi3
                0x000000f8                __aeabi_uidiv
                0x00000204                __aeabi_uidivmod
 .text          0x0000020c      0x1d4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
                0x0000020c                __aeabi_idiv
                0x0000020c                __divsi3
                0x000003d8                __aeabi_idivmod
 .text          0x000003e0        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_dvmd_tls.o)
                0x000003e0                __aeabi_ldiv0
                0x000003e0                __aeabi_idiv0
 .text          0x000003e4       0x7c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o)
                0x000003e4                __aeabi_cdrcmple
                0x000003f4                __aeabi_cdcmple
                0x000003f4                __aeabi_cdcmpeq
                0x00000404                __aeabi_dcmpeq
                0x00000410                __aeabi_dcmplt
                0x00000424                __aeabi_dcmple
                0x00000438                __aeabi_dcmpgt
                0x0000044c                __aeabi_dcmpge
 .text          0x00000460       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_clzsi2.o)
                0x00000460                __clzsi2
 *(.text*)
 .text.deregister_tm_clones
                0x0000049c       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .text.register_tm_clones
                0x000004bc       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .text.__do_global_dtors_aux
                0x000004e4       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .text.frame_dummy
                0x00000510       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .text.__cvt    0x00000534       0xcc /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
                0x00000534                __cvt
 .text.__exponent
                0x00000600       0x82 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
                0x00000600                __exponent
 *fill*         0x00000682        0x2 
 .text._printf_float
                0x00000684      0x45c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
                0x00000684                _printf_float
 .text._printf_common
                0x00000ae0       0xde /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
                0x00000ae0                _printf_common
 *fill*         0x00000bbe        0x2 
 .text._printf_i
                0x00000bc0      0x218 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
                0x00000bc0                _printf_i
 .text.std      0x00000dd8       0x6c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.stdio_exit_handler
                0x00000e44       0x1c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.cleanup_stdio
                0x00000e60       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.global_stdio_init.part.0
                0x00000e9c       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_acquire
                0x00000ed8       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                0x00000ed8                __sfp_lock_acquire
 .text.__sfp_lock_release
                0x00000ee8       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                0x00000ee8                __sfp_lock_release
 .text.__sinit  0x00000ef8       0x30 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                0x00000ef8                __sinit
 .text._fwalk_sglue
                0x00000f28       0x38 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
                0x00000f28                _fwalk_sglue
 .text.__sread  0x00000f60       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
                0x00000f60                __sread
 .text.__swrite
                0x00000f88       0x38 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
                0x00000f88                __swrite
 .text.__sseek  0x00000fc0       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
                0x00000fc0                __sseek
 .text.__sclose
                0x00000fec        0xc /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
                0x00000fec                __sclose
 .text.memset   0x00000ff8       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
                0x00000ff8                memset
 .text._localeconv_r
                0x00001008        0x8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
                0x00001008                _localeconv_r
 .text._close_r
                0x00001010       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
                0x00001010                _close_r
 .text._lseek_r
                0x00001034       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
                0x00001034                _lseek_r
 .text._read_r  0x0000105c       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
                0x0000105c                _read_r
 .text._write_r
                0x00001084       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
                0x00001084                _write_r
 .text.__retarget_lock_init_recursive
                0x000010ac        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
                0x000010ac                __retarget_lock_init_recursive
 .text.__retarget_lock_acquire_recursive
                0x000010ae        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
                0x000010ae                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x000010b0        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
                0x000010b0                __retarget_lock_release_recursive
 .text.memchr   0x000010b2       0x16 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
                0x000010b2                memchr
 .text.quorem   0x000010c8      0x10a /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 *fill*         0x000011d2        0x2 
 .text._dtoa_r  0x000011d4      0xbd8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
                0x000011d4                _dtoa_r
 .text._free_r  0x00001dac       0x94 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
                0x00001dac                _free_r
 .text.malloc   0x00001e40       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
                0x00001e40                malloc
 .text.sbrk_aligned
                0x00001e54       0x44 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
 .text._malloc_r
                0x00001e98      0x100 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
                0x00001e98                _malloc_r
 .text.__malloc_lock
                0x00001f98       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
                0x00001f98                __malloc_lock
 .text.__malloc_unlock
                0x00001fa8       0x10 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
                0x00001fa8                __malloc_unlock
 .text._Balloc  0x00001fb8       0x84 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00001fb8                _Balloc
 .text._Bfree   0x0000203c       0x48 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x0000203c                _Bfree
 .text.__multadd
                0x00002084       0x88 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00002084                __multadd
 .text.__hi0bits
                0x0000210c       0x42 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x0000210c                __hi0bits
 .text.__lo0bits
                0x0000214e       0x5c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x0000214e                __lo0bits
 *fill*         0x000021aa        0x2 
 .text.__i2b    0x000021ac       0x30 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x000021ac                __i2b
 .text.__multiply
                0x000021dc      0x158 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x000021dc                __multiply
 .text.__pow5mult
                0x00002334       0xc4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00002334                __pow5mult
 .text.__lshift
                0x000023f8       0xd8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x000023f8                __lshift
 .text.__mcmp   0x000024d0       0x36 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x000024d0                __mcmp
 *fill*         0x00002506        0x2 
 .text.__mdiff  0x00002508      0x148 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00002508                __mdiff
 .text.__d2b    0x00002650       0xc0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00002650                __d2b
 .text.__sflush_r
                0x00002710      0x114 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
                0x00002710                __sflush_r
 .text._fflush_r
                0x00002824       0x56 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
                0x00002824                _fflush_r
 *fill*         0x0000287a        0x2 
 .text._sbrk_r  0x0000287c       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
                0x0000287c                _sbrk_r
 .text.memcpy   0x000028a0       0x12 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
                0x000028a0                memcpy
 *fill*         0x000028b2        0x2 
 .text.__assert_func
                0x000028b4       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
                0x000028b4                __assert_func
 .text._calloc_r
                0x000028f0       0x5a /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
                0x000028f0                _calloc_r
 .text.__ascii_mbtowc
                0x0000294a       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
                0x0000294a                __ascii_mbtowc
 .text.__ascii_wctomb
                0x0000296e       0x1a /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
                0x0000296e                __ascii_wctomb
 .text.fprintf  0x00002988       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
                0x00002988                fprintf
                0x00002988                fiprintf
 .text.abort    0x000029a8        0xe /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
                0x000029a8                abort
 .text.__sfputc_r
                0x000029b6       0x2a /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .text.__sfputs_r
                0x000029e0       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
                0x000029e0                __sfputs_r
 .text._vfprintf_r
                0x00002a04      0x240 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
                0x00002a04                _vfiprintf_r
                0x00002a04                _vfprintf_r
 .text.__swbuf_r
                0x00002c44       0x84 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
                0x00002c44                __swbuf_r
 .text.__swsetup_r
                0x00002cc8       0xb4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
                0x00002cc8                __swsetup_r
 .text._raise_r
                0x00002d7c       0x52 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
                0x00002d7c                _raise_r
 *fill*         0x00002dce        0x2 
 .text.raise    0x00002dd0       0x14 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
                0x00002dd0                raise
 .text._kill_r  0x00002de4       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
                0x00002de4                _kill_r
 .text._getpid_r
                0x00002e08        0x8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
                0x00002e08                _getpid_r
 .text.__swhatbuf_r
                0x00002e10       0x54 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
                0x00002e10                __swhatbuf_r
 .text.__smakebuf_r
                0x00002e64       0x72 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
                0x00002e64                __smakebuf_r
 *fill*         0x00002ed6        0x2 
 .text._fstat_r
                0x00002ed8       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
                0x00002ed8                _fstat_r
 .text._isatty_r
                0x00002efc       0x24 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
                0x00002efc                _isatty_r
 .text.__NVIC_EnableIRQ
                0x00002f20       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.__NVIC_GetEnableIRQ
                0x00002f54       0x3c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.__NVIC_DisableIRQ
                0x00002f90       0x44 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.__NVIC_ClearPendingIRQ
                0x00002fd4       0x38 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.__NVIC_SetPriority
                0x0000300c       0xdc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_GPIO_togglePins
                0x000030e8       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_Timer_reset
                0x00003108       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_Timer_getTimerCount
                0x00003128       0x18 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_Timer_startCounter
                0x00003140       0x24 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_Timer_disableInterrupt
                0x00003164       0x28 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_Timer_getEnabledInterrupts
                0x0000318c       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.DL_Timer_getPendingInterrupt
                0x000031ac       0x1a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 *fill*         0x000031c6        0x2 
 .text.DL_Timer_clearInterruptStatus
                0x000031c8       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .text.main     0x000031e8       0xc4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
                0x000031e8                main
 .text.TIMG0_IRQHandler
                0x000032ac       0x38 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
                0x000032ac                TIMG0_IRQHandler
 .text.TIMG8_IRQHandler
                0x000032e4       0x44 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
                0x000032e4                TIMG8_IRQHandler
 .text.__NVIC_SetPriority
                0x00003328       0xdc CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_Common_updateReg
                0x00003404       0x32 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 *fill*         0x00003436        0x2 
 .text.DL_SYSCTL_setBORThreshold
                0x00003438       0x28 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_SYSCTL_setMCLKDivider
                0x00003460       0x30 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_SYSCTL_setSYSOSCFreq
                0x00003490       0x2c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_SYSCTL_setULPCLKDivider
                0x000034bc       0x30 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_SYSCTL_disableSYSPLL
                0x000034ec       0x28 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_SYSCTL_disableHFXT
                0x00003514       0x24 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_GPIO_enablePower
                0x00003538       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_GPIO_reset
                0x00003558       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_GPIO_initDigitalOutput
                0x00003578       0x24 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_GPIO_clearPins
                0x0000359c       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_GPIO_enableOutput
                0x000035b8       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_Timer_enablePower
                0x000035d8       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_Timer_reset
                0x000035f8       0x20 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_Timer_enableClock
                0x00003618       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.DL_Timer_enableInterrupt
                0x00003634       0x28 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .text.SYSCFG_DL_init
                0x0000365c       0x1a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
                0x0000365c                SYSCFG_DL_init
 *fill*         0x00003676        0x2 
 .text.SYSCFG_DL_initPower
                0x00003678       0x4c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
                0x00003678                SYSCFG_DL_initPower
 .text.SYSCFG_DL_GPIO_init
                0x000036c4       0x30 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
                0x000036c4                SYSCFG_DL_GPIO_init
 .text.SYSCFG_DL_SYSCTL_init
                0x000036f4       0x2a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
                0x000036f4                SYSCFG_DL_SYSCTL_init
 *fill*         0x0000371e        0x2 
 .text.SYSCFG_DL_TIMER_0_init
                0x00003720       0x48 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
                0x00003720                SYSCFG_DL_TIMER_0_init
 .text.Reset_Handler
                0x00003768       0x60 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
                0x00003768                Reset_Handler
 .text.Default_Handler
                0x000037c8       0x24 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
                0x000037c8                TIMG6_IRQHandler
                0x000037c8                HardFault_Handler
                0x000037c8                SysTick_Handler
                0x000037c8                PendSV_Handler
                0x000037c8                NMI_Handler
                0x000037c8                UART1_IRQHandler
                0x000037c8                TIMA1_IRQHandler
                0x000037c8                ADC0_IRQHandler
                0x000037c8                SPI1_IRQHandler
                0x000037c8                ADC1_IRQHandler
                0x000037c8                GROUP1_IRQHandler
                0x000037c8                GROUP0_IRQHandler
                0x000037c8                UART2_IRQHandler
                0x000037c8                Default_Handler
                0x000037c8                SVC_Handler
                0x000037c8                TIMG12_IRQHandler
                0x000037c8                DAC0_IRQHandler
                0x000037c8                UART_0_INST_IRQHandler
                0x000037c8                TIMG7_IRQHandler
                0x000037c8                SPI0_IRQHandler
                0x000037c8                TIMA0_IRQHandler
                0x000037c8                UART3_IRQHandler
                0x000037c8                CANFD0_IRQHandler
 .text.SystemInit
                0x000037ec        0xa CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
                0x000037ec                SystemInit
 .text._exit    0x000037f6        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x000037f6                _exit
 .text._close   0x00003802       0x14 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x00003802                _close
 .text._fstat   0x00003816       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x00003816                _fstat
 .text._getpid  0x00003832        0xc CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x00003832                _getpid
 .text._isatty  0x0000383e       0x12 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x0000383e                _isatty
 .text._kill    0x00003850       0x20 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x00003850                _kill
 .text._lseek   0x00003870       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x00003870                _lseek
 .text._read    0x00003886       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x00003886                _read
 .text._sbrk    0x0000389c       0x5c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x0000389c                _sbrk
 .text._write   0x000038f8       0x16 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
                0x000038f8                _write
 .text.DL_Common_delayCycles
                0x0000390e       0x1c lib/libti_mspm0_driverlib.a(dl_common.c.obj)
                0x0000390e                DL_Common_delayCycles
 .text.DL_Common_updateReg
                0x0000392a       0x32 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setLoadValue
                0x0000395c       0x20 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setCounterValueAfterEnable
                0x0000397c       0x2c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .text.DL_Timer_setClockConfig
                0x000039a8       0x40 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
                0x000039a8                DL_Timer_setClockConfig
 .text.DL_Timer_initTimerMode
                0x000039e8       0xa4 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
                0x000039e8                DL_Timer_initTimerMode
 .text.DL_Timer_setCaptureCompareValue
                0x00003a8c       0x38 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
                0x00003a8c                DL_Timer_setCaptureCompareValue
 .text.DL_Timer_setCaptureCompareCtl
                0x00003ac4       0x4c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
                0x00003ac4                DL_Timer_setCaptureCompareCtl
 .text.__aeabi_dadd
                0x00003b10      0x834 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
                0x00003b10                __aeabi_dadd
 .text.__aeabi_ddiv
                0x00004344      0x63c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
                0x00004344                __aeabi_ddiv
 .text.__eqdf2  0x00004980       0x8c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
                0x00004980                __eqdf2
                0x00004980                __nedf2
 .text.__gedf2  0x00004a0c       0xe0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
                0x00004a0c                __gtdf2
                0x00004a0c                __gedf2
 .text.__ledf2  0x00004aec       0xdc /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
                0x00004aec                __ltdf2
                0x00004aec                __ledf2
 .text.__aeabi_dmul
                0x00004bc8      0x590 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
                0x00004bc8                __aeabi_dmul
 .text.__aeabi_dsub
                0x00005158      0x870 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
                0x00005158                __aeabi_dsub
 .text.__aeabi_dcmpun
                0x000059c8       0x44 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
                0x000059c8                __aeabi_dcmpun
 .text.__aeabi_d2iz
                0x00005a0c       0x78 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
                0x00005a0c                __aeabi_d2iz
 .text.__aeabi_i2d
                0x00005a84       0x5c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
                0x00005a84                __aeabi_i2d
 .text.__aeabi_ui2d
                0x00005ae0       0x48 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
                0x00005ae0                __aeabi_ui2d
 .text.__errno  0x00005b28        0xc /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)
                0x00005b28                __errno
 *(.glue_7)
 .glue_7        0x00005b34        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x00005b34        0x0 linker stubs
 *(.eh_frame)
 .eh_frame      0x00005b34        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 *(.init)
 .init          0x00005b34        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
                0x00005b34                _init
 .init          0x00005b38        0x8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o
 *(.fini)
 .fini          0x00005b40        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
                0x00005b40                _fini
 .fini          0x00005b44        0x8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o
                0x00005b4c                        . = ALIGN (0x4)
                0x00005b4c                        _etext = .

.vfp11_veneer   0x00005b4c        0x0
 .vfp11_veneer  0x00005b4c        0x0 linker stubs

.v4_bx          0x00005b4c        0x0
 .v4_bx         0x00005b4c        0x0 linker stubs

.iplt           0x00005b4c        0x0
 .iplt          0x00005b4c        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o

.rodata         0x00005b50      0x3cc
                0x00005b50                        . = ALIGN (0x4)
 *(.rodata)
 *(.rodata*)
 .rodata._printf_float.str1.1
                0x00005b50      0x173 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
                                 0x12 (size before relaxing)
 .rodata._printf_i.str1.1
                0x00005cc3       0x22 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
 .rodata._dtoa_r.str1.1
                0x00005cc3       0x90 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 .rodata._Balloc.str1.1
                0x00005cc3       0x71 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .rodata.__multadd.str1.1
                0x00005cc3       0x11 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 *fill*         0x00005cc3        0x1 
 .rodata.p05.0  0x00005cc4        0xc /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .rodata.__mprec_bigtens
                0x00005cd0       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00005cd0                __mprec_bigtens
 .rodata.__mprec_tens
                0x00005cf8       0xc8 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
                0x00005cf8                __mprec_tens
 .rodata._setlocale_r.str1.1
                0x00005dc0        0x9 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .rodata.str1.1
                0x00005dc0        0x2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .rodata.__assert_func.str1.1
                0x00005dc0       0x3d /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .rodata._ctype_
                0x00005dc0      0x101 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-ctype_.o)
                0x00005dc0                _ctype_
 .rodata._vfprintf_r.str1.1
                0x00005ec1       0x11 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 *fill*         0x00005ec1        0x3 
 .rodata.gTIMER_0ClockConfig
                0x00005ec4        0x3 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 *fill*         0x00005ec7        0x1 
 .rodata.gTIMER_0TimerConfig
                0x00005ec8       0x14 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .rodata.__aeabi_ddiv
                0x00005edc       0x40 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
                0x00005f1c                        . = ALIGN (0x4)

.rel.dyn        0x00005f1c        0x0
 .rel.iplt      0x00005f1c        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o

.ARM.extab
 *(.ARM.extab* .gnu.linkonce.armextab.*)

.ARM            0x00005f1c        0x0
                0x00005f1c                        __exidx_start = .
 *(.ARM.exidx*)
                0x00005f1c                        __exidx_end = .

.preinit_array  0x00005f1c        0x0
                0x00005f1c                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array*)
                0x00005f1c                        PROVIDE (__preinit_array_end = .)

.init_array     0x00005f1c        0x4
                0x00005f1c                        PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array*)
 .init_array    0x00005f1c        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
                0x00005f20                        PROVIDE (__init_array_end = .)

.fini_array     0x00005f20        0x4
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array*)
 .fini_array    0x00005f20        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
                [!provide]                        PROVIDE (__fini_array_end = .)
                0x00005f24                        _sidata = LOADADDR (.data)

.data           0x20000000      0x1c8 load address 0x00005f24
                0x20000000                        . = ALIGN (0x4)
                0x20000000                        _sdata = .
 *(.data)
 *(.data*)
 .data.__sglue  0x20000000        0xc /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                0x20000000                __sglue
 .data._impure_ptr
                0x2000000c        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
                0x2000000c                _impure_ptr
 .data._impure_data
                0x20000010       0x4c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
                0x20000010                _impure_data
 .data.__global_locale
                0x2000005c      0x16c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
                0x2000005c                __global_locale
                0x200001c8                        . = ALIGN (0x4)
                0x200001c8                        _edata = .

.tm_clone_table
                0x200001c8        0x0 load address 0x000060ec
 .tm_clone_table
                0x200001c8        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .tm_clone_table
                0x200001c8        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o

.igot.plt       0x200001c8        0x0 load address 0x000060ec
 .igot.plt      0x200001c8        0x0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o

.bss            0x200001c8      0x170 load address 0x000060ec
                0x200001c8                        . = ALIGN (0x4)
                0x200001c8                        _sbss = .
                0x200001c8                        __bss_start__ = _sbss
 *(.bss)
 *(.bss*)
 .bss.completed.1
                0x200001c8        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 *fill*         0x200001c9        0x3 
 .bss.object.0  0x200001cc       0x18 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .bss.__sf      0x200001e4      0x138 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                0x200001e4                __sf
 .bss.__stdio_exit_handler
                0x2000031c        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
                0x2000031c                __stdio_exit_handler
 .bss.errno     0x20000320        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
                0x20000320                errno
 .bss.__lock___malloc_recursive_mutex
                0x20000324        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
                0x20000324                __lock___malloc_recursive_mutex
 .bss.__lock___sfp_recursive_mutex
                0x20000325        0x1 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
                0x20000325                __lock___sfp_recursive_mutex
 *fill*         0x20000326        0x2 
 .bss.__malloc_sbrk_start
                0x20000328        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
                0x20000328                __malloc_sbrk_start
 .bss.__malloc_free_list
                0x2000032c        0x4 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
                0x2000032c                __malloc_free_list
 .bss.uwTick    0x20000330        0x4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
                0x20000330                uwTick
 .bss.heap_end.0
                0x20000334        0x4 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 *(COMMON)
                0x20000338                        . = ALIGN (0x4)
                0x20000338                        _ebss = .
                0x20000338                        __bss_end__ = _ebss

._user_heap_stack
                0x20000338     0x2000 load address 0x000060ec
                0x20000338                        . = ALIGN (0x8)
                0x20000338                        PROVIDE (end = .)
                [!provide]                        PROVIDE (_end = .)
                0x20001338                        . = (. + _heap_size)
 *fill*         0x20000338     0x1000 
                0x20002338                        . = (. + _stack_size)
 *fill*         0x20001338     0x1000 
                0x20002338                        . = ALIGN (0x8)
                0x20002338                        _sstack = .

/DISCARD/
 libc.a(*)
 libm.a(*)
 libgcc.a(*)

.ARM.attributes
                0x00000000       0x28
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crti.o
 .ARM.attributes
                0x0000001e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtbegin.o
 .ARM.attributes
                0x0000004a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
 .ARM.attributes
                0x00000076       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
 .ARM.attributes
                0x000000a2       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .ARM.attributes
                0x000000ce       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
 .ARM.attributes
                0x000000fa       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
 .ARM.attributes
                0x00000126       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
 .ARM.attributes
                0x00000152       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .ARM.attributes
                0x0000017e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
 .ARM.attributes
                0x000001aa       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
 .ARM.attributes
                0x000001d6       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-impure.o)
 .ARM.attributes
                0x00000202       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
 .ARM.attributes
                0x0000022e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
 .ARM.attributes
                0x0000025a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
 .ARM.attributes
                0x00000286       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .ARM.attributes
                0x000002b2       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
 .ARM.attributes
                0x000002de       0x1c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strlen.o)
 .ARM.attributes
                0x000002fa       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 .ARM.attributes
                0x00000326       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
 .ARM.attributes
                0x00000352       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
 .ARM.attributes
                0x0000037e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
 .ARM.attributes
                0x000003aa       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
 .ARM.attributes
                0x000003d6       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .ARM.attributes
                0x00000402       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
 .ARM.attributes
                0x0000042e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .ARM.attributes
                0x0000045a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
 .ARM.attributes
                0x00000486       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
 .ARM.attributes
                0x000004b2       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .ARM.attributes
                0x000004de       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
 .ARM.attributes
                0x0000050a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
 .ARM.attributes
                0x00000536       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
 .ARM.attributes
                0x00000562       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-ctype_.o)
 .ARM.attributes
                0x0000058e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
 .ARM.attributes
                0x000005ba       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
 .ARM.attributes
                0x000005e6       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .ARM.attributes
                0x00000612       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
 .ARM.attributes
                0x0000063e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
 .ARM.attributes
                0x0000066a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .ARM.attributes
                0x00000696       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
 .ARM.attributes
                0x000006c2       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
 .ARM.attributes
                0x000006ee       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
 .ARM.attributes
                0x0000071a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
 .ARM.attributes
                0x00000746       0x2c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .ARM.attributes
                0x00000772       0x2c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .ARM.attributes
                0x0000079e       0x2c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .ARM.attributes
                0x000007ca       0x2c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .ARM.attributes
                0x000007f6       0x2c lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .ARM.attributes
                0x00000822       0x2c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .ARM.attributes
                0x0000084e       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_uqi.o)
 .ARM.attributes
                0x0000086c       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_shi.o)
 .ARM.attributes
                0x0000088a       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
 .ARM.attributes
                0x000008a8       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
 .ARM.attributes
                0x000008c6       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x000008e4       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x00000902       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
 .ARM.attributes
                0x0000092e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
 .ARM.attributes
                0x0000095a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
 .ARM.attributes
                0x00000986       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
 .ARM.attributes
                0x000009b2       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
 .ARM.attributes
                0x000009de       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
 .ARM.attributes
                0x00000a0a       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
 .ARM.attributes
                0x00000a36       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
 .ARM.attributes
                0x00000a62       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
 .ARM.attributes
                0x00000a8e       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
 .ARM.attributes
                0x00000aba       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
 .ARM.attributes
                0x00000ae6       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_clzsi2.o)
 .ARM.attributes
                0x00000b04       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)
 .ARM.attributes
                0x00000b30       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtend.o
 .ARM.attributes
                0x00000b5c       0x1e /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/crtn.o
OUTPUT(bin/mspm0_timer_test2.elf elf32-littlearm)
LOAD linker stubs
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libm.a
LOAD /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a

.debug_frame    0x00000000     0x1e7c
 .debug_frame   0x00000000       0x70 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_float.o)
 .debug_frame   0x00000070       0x54 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf_i.o)
 .debug_frame   0x000000c4      0x138 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-findfp.o)
 .debug_frame   0x000001fc       0x34 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fwalk.o)
 .debug_frame   0x00000230       0x90 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-stdio.o)
 .debug_frame   0x000002c0       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memset.o)
 .debug_frame   0x000002e0       0x40 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-localeconv.o)
 .debug_frame   0x00000320       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-closer.o)
 .debug_frame   0x0000034c       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-reent.o)
 .debug_frame   0x00000378       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lseekr.o)
 .debug_frame   0x000003a4       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-readr.o)
 .debug_frame   0x000003d0       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-writer.o)
 .debug_frame   0x000003fc       0xb0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-lock.o)
 .debug_frame   0x000004ac       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memchr-stub.o)
 .debug_frame   0x000004cc       0x54 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-dtoa.o)
 .debug_frame   0x00000520       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-freer.o)
 .debug_frame   0x0000054c       0x40 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-malloc.o)
 .debug_frame   0x0000058c       0x50 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mallocr.o)
 .debug_frame   0x000005dc       0x40 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mlock.o)
 .debug_frame   0x0000061c      0x234 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mprec.o)
 .debug_frame   0x00000850       0x68 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fflush.o)
 .debug_frame   0x000008b8       0x54 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-locale.o)
 .debug_frame   0x0000090c       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-sbrkr.o)
 .debug_frame   0x00000938       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-memcpy-stub.o)
 .debug_frame   0x00000960       0x4c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-assert.o)
 .debug_frame   0x000009ac       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-callocr.o)
 .debug_frame   0x000009d8       0x44 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-mbtowc_r.o)
 .debug_frame   0x00000a1c       0x38 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wctomb_r.o)
 .debug_frame   0x00000a54       0x50 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fprintf.o)
 .debug_frame   0x00000aa4       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-abort.o)
 .debug_frame   0x00000acc       0x9c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-nano-vfprintf.o)
 .debug_frame   0x00000b68       0x48 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wbuf.o)
 .debug_frame   0x00000bb0       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-wsetup.o)
 .debug_frame   0x00000bdc       0xe0 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signal.o)
 .debug_frame   0x00000cbc       0x44 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-signalr.o)
 .debug_frame   0x00000d00       0x54 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-makebuf.o)
 .debug_frame   0x00000d54       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-fstatr.o)
 .debug_frame   0x00000d80       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-isattyr.o)
 .debug_frame   0x00000dac      0x208 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_frame   0x00000fb4      0x2b4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_frame   0x00001268       0x6c CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_frame   0x000012d4      0x248 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_frame   0x0000151c       0x30 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_frame   0x0000154c      0x678 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_frame   0x00001bc4       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
 .debug_frame   0x00001be4       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
 .debug_frame   0x00001c04       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(adddf3.o)
 .debug_frame   0x00001c40       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(divdf3.o)
 .debug_frame   0x00001c7c       0x38 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(eqdf2.o)
 .debug_frame   0x00001cb4       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(gedf2.o)
 .debug_frame   0x00001cf0       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(ledf2.o)
 .debug_frame   0x00001d2c       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(muldf3.o)
 .debug_frame   0x00001d68       0x3c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(subdf3.o)
 .debug_frame   0x00001da4       0x34 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(unorddf2.o)
 .debug_frame   0x00001dd8       0x30 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(fixdfsi.o)
 .debug_frame   0x00001e08       0x2c /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatsidf.o)
 .debug_frame   0x00001e34       0x28 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(floatunsidf.o)
 .debug_frame   0x00001e5c       0x20 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libg_nano.a(libc_a-errno.o)

.debug_line_str
                0x00000000      0x1ae
 .debug_line_str
                0x00000000      0x1ae /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/thumb/v6-m/nofp/libc_nano.a(libc_a-strlen.o)
                                 0xe0 (size before relaxing)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_uqi.o)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_thumb1_case_shi.o)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_udivsi3.o)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_divsi3.o)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_dvmd_tls.o)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_arm_cmpdf2.o)
 .debug_line_str
                0x000001ae       0xc2 /usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v6-m/nofp/libgcc.a(_clzsi2.o)

.debug_info     0x00000000    0x232a9
 .debug_info    0x00000000     0x6cc7 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_info    0x00006cc7     0x70f1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_info    0x0000ddb8     0x66b4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_info    0x0001446c      0x6d9 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_info    0x00014b45     0x6599 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_info    0x0001b0de     0x81cb lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.debug_abbrev   0x00000000      0xf0c
 .debug_abbrev  0x00000000      0x2d4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_abbrev  0x000002d4      0x313 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_abbrev  0x000005e7      0x29b CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_abbrev  0x00000882      0x1aa CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_abbrev  0x00000a2c      0x1d6 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_abbrev  0x00000c02      0x30a lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.debug_aranges  0x00000000      0x408
 .debug_aranges
                0x00000000       0x98 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_aranges
                0x00000098       0xc8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_aranges
                0x00000160       0x30 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_aranges
                0x00000190       0xa8 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_aranges
                0x00000238       0x20 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_aranges
                0x00000258      0x1b0 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.debug_rnglists
                0x00000000      0x2f8
 .debug_rnglists
                0x00000000       0x6f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_rnglists
                0x0000006f       0x92 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_rnglists
                0x00000101       0x1f CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_rnglists
                0x00000120       0x79 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_rnglists
                0x00000199       0x13 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_rnglists
                0x000001ac      0x14c lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.debug_macro    0x00000000    0x2916c
 .debug_macro   0x00000000      0x4b4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000004b4      0xac0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000f74       0x22 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000f96       0x75 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000100b       0x24 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000102f       0x94 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000010c3       0x3c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000010ff       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001133       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001149       0x57 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000011a0       0x9e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000123e      0x364 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000015a2      0x112 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000016b4       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000016c4       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000016da       0x4a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001724       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001758       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001768       0x58 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000017c0      0x1e5 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000019a5       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000019bb       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000019d1      0x170 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001b41      0x103 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001c44       0x6a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001cae      0x1df CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001e8d       0x16 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001ea3       0x3c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001edf       0x86 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001f65       0x22 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001f87      0x125 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000020ac       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000020c8       0x22 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000020ea       0xd1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000021bb      0x4da CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00002695      0x11f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000027b4     0x2a9e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00005252      0x7fa CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00005a4c      0x8c0 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000630c      0x22d CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00006539      0xd04 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000723d     0x226c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000094a9      0xee9 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0000a392     0x5cc6 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00010058     0x3636 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001368e     0x24a8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00015b36      0x154 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00015c8a      0x33f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00015fc9     0x24ec CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000184b5      0x281 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00018736     0x2cd8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001b40e      0xea7 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001c2b5      0x44c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001c701     0x1617 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001dd18      0x19f CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001deb7       0x46 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001defd      0x3c7 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001e2c4      0x3ac CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001e670      0x265 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001e8d5      0x47e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0001ed53     0x2ea1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00021bf4      0xf0e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00022b02      0x19b CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00022c9d       0x34 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00022cd1      0x320 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00022ff1       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0002300d       0x2e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0002303b       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0002304b       0x7a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000230c5      0x164 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00023229      0x1be CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000233e7       0xcd CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000234b4      0x7de CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00023c92      0x17c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00023e0e       0x52 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00023e60      0x235 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00024095       0x57 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000240ec      0x56b CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00024657       0x70 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000246c7      0x4e8 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00024baf      0x6e1 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00025290      0x42b CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000256bb       0x2e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000256e9       0xbd CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000257a6      0x747 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00025eed      0x578 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00026465       0x2e CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00026493       0xbe CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00026551       0x10 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x00026561       0x1c CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x0002657d       0x3a CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_macro   0x000265b7      0x4b4 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_macro   0x00026a6b      0x4b4 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_macro   0x00026f1f      0x200 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002711f      0x190 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000272af       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000272bf       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000272cf       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000272df       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000272ef       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002730b       0x52 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002735d       0x22 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002737f       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002738f       0x52 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000273e1       0xcf CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000274b0       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000274cc       0x3d CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00027509       0x35 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002753e      0x12c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x0002766a      0x237 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000278a1       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x000278b1      0x242 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00027af3       0xce CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00027bc1       0x1c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00027bdd       0x10 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_macro   0x00027bed      0x1dc lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00027dc9      0xaba lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00028883       0x8e lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00028911       0x51 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_macro   0x00028962      0x1fd lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00028b5f      0x12b lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
 .debug_macro   0x00028c8a      0x4e2 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.debug_line     0x00000000     0x4874
 .debug_line    0x00000000      0xe37 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
 .debug_line    0x00000e37      0xe39 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_line    0x00001c70      0xbf2 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_line    0x00002862      0x5b4 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_line    0x00002e16      0x6e6 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_line    0x000034fc     0x1378 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.debug_str      0x00000000   0x10e420
 .debug_str     0x00000000   0x10e420 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
                             0x10b723 (size before relaxing)
 .debug_str     0x0010e420   0x10bfea CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .debug_str     0x0010e420   0x10b1a6 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .debug_str     0x0010e420     0x8a8c CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .debug_str     0x0010e420    0xe7a91 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .debug_str     0x0010e420    0xf2012 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)

.comment        0x00000000       0x45
 .comment       0x00000000       0x45 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj
                                 0x46 (size before relaxing)
 .comment       0x00000045       0x46 CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj
 .comment       0x00000045       0x46 CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj
 .comment       0x00000045       0x46 CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj
 .comment       0x00000045       0x46 lib/libti_mspm0_driverlib.a(dl_common.c.obj)
 .comment       0x00000045       0x46 lib/libti_mspm0_driverlib.a(dl_timer.c.obj)
