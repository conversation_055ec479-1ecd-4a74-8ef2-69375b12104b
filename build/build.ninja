# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: mspm0_timer_test2
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/
# =============================================================================
# Object build statements for EXECUTABLE target mspm0_timer_test2


#############################################
# Order-only phony target for mspm0_timer_test2

build cmake_object_order_depends_target_mspm0_timer_test2: phony || cmake_object_order_depends_target_ti_mspm0_driverlib

build CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj: C_COMPILER__mspm0_timer_test2_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Src/main.c || cmake_object_order_depends_target_mspm0_timer_test2
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__
  DEP_FILE = CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = CMakeFiles/mspm0_timer_test2.dir
  OBJECT_FILE_DIR = CMakeFiles/mspm0_timer_test2.dir/code/User/Src
  TARGET_COMPILE_PDB = CMakeFiles/mspm0_timer_test2.dir/
  TARGET_PDB = bin/mspm0_timer_test2.pdb

build CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj: C_COMPILER__mspm0_timer_test2_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Src/hardware_init.c || cmake_object_order_depends_target_mspm0_timer_test2
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__
  DEP_FILE = CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = CMakeFiles/mspm0_timer_test2.dir
  OBJECT_FILE_DIR = CMakeFiles/mspm0_timer_test2.dir/code/User/Src
  TARGET_COMPILE_PDB = CMakeFiles/mspm0_timer_test2.dir/
  TARGET_PDB = bin/mspm0_timer_test2.pdb

build CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj: C_COMPILER__mspm0_timer_test2_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/startup_mspm0g3507.c || cmake_object_order_depends_target_mspm0_timer_test2
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__
  DEP_FILE = CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = CMakeFiles/mspm0_timer_test2.dir
  OBJECT_FILE_DIR = CMakeFiles/mspm0_timer_test2.dir/cmake
  TARGET_COMPILE_PDB = CMakeFiles/mspm0_timer_test2.dir/
  TARGET_PDB = bin/mspm0_timer_test2.pdb

build CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj: C_COMPILER__mspm0_timer_test2_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/syscalls.c || cmake_object_order_depends_target_mspm0_timer_test2
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__
  DEP_FILE = CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = CMakeFiles/mspm0_timer_test2.dir
  OBJECT_FILE_DIR = CMakeFiles/mspm0_timer_test2.dir/cmake
  TARGET_COMPILE_PDB = CMakeFiles/mspm0_timer_test2.dir/
  TARGET_PDB = bin/mspm0_timer_test2.pdb


# =============================================================================
# Link build statements for EXECUTABLE target mspm0_timer_test2


#############################################
# Link the executable bin/mspm0_timer_test2.elf

build bin/mspm0_timer_test2.elf: C_EXECUTABLE_LINKER__mspm0_timer_test2_Debug CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj | lib/libti_mspm0_driverlib.a || lib/libti_mspm0_driverlib.a
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb
  LINK_FLAGS = -mcpu=cortex-m0plus -mthumb -T/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/mspm0g3507.ld -Wl,--gc-sections -Wl,--print-memory-usage -Wl,-Map=mspm0_timer_test2.map --specs=nano.specs -u _printf_float -Wl,--start-group -lc -lm -Wl,--end-group -Wl,--no-warn-rwx-segments -Wl,--print-memory-usage
  LINK_LIBRARIES = lib/libti_mspm0_driverlib.a  -lm
  OBJECT_DIR = CMakeFiles/mspm0_timer_test2.dir
  POST_BUILD = cd /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build && arm-none-eabi-objcopy -O binary /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/bin/mspm0_timer_test2.elf bin/mspm0_timer_test2.bin && arm-none-eabi-objcopy -O ihex /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/bin/mspm0_timer_test2.elf bin/mspm0_timer_test2.hex && arm-none-eabi-size /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/bin/mspm0_timer_test2.elf
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/mspm0_timer_test2.dir/
  TARGET_FILE = bin/mspm0_timer_test2.elf
  TARGET_PDB = bin/mspm0_timer_test2.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build && /usr/local/bin/ccmake -S/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2 -B/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build && /usr/local/bin/cmake --regenerate-during-build -S/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2 -B/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target ti_mspm0_driverlib


#############################################
# Order-only phony target for ti_mspm0_driverlib

build cmake_object_order_depends_target_ti_mspm0_driverlib: phony || .

build cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_common.c.obj: C_COMPILER__ti_mspm0_driverlib_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_common.c || cmake_object_order_depends_target_ti_mspm0_driverlib
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__
  DEP_FILE = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_common.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir
  OBJECT_FILE_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib
  TARGET_COMPILE_PDB = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/ti_mspm0_driverlib.pdb
  TARGET_PDB = lib/libti_mspm0_driverlib.pdb

build cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_uart.c.obj: C_COMPILER__ti_mspm0_driverlib_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_uart.c || cmake_object_order_depends_target_ti_mspm0_driverlib
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__
  DEP_FILE = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_uart.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir
  OBJECT_FILE_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib
  TARGET_COMPILE_PDB = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/ti_mspm0_driverlib.pdb
  TARGET_PDB = lib/libti_mspm0_driverlib.pdb

build cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_timer.c.obj: C_COMPILER__ti_mspm0_driverlib_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_timer.c || cmake_object_order_depends_target_ti_mspm0_driverlib
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__
  DEP_FILE = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_timer.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir
  OBJECT_FILE_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib
  TARGET_COMPILE_PDB = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/ti_mspm0_driverlib.pdb
  TARGET_PDB = lib/libti_mspm0_driverlib.pdb

build cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_vref.c.obj: C_COMPILER__ti_mspm0_driverlib_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_vref.c || cmake_object_order_depends_target_ti_mspm0_driverlib
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__
  DEP_FILE = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_vref.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir
  OBJECT_FILE_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib
  TARGET_COMPILE_PDB = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/ti_mspm0_driverlib.pdb
  TARGET_PDB = lib/libti_mspm0_driverlib.pdb

build cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/m0p/dl_interrupt.c.obj: C_COMPILER__ti_mspm0_driverlib_unscanned_Debug /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p/dl_interrupt.c || cmake_object_order_depends_target_ti_mspm0_driverlib
  DEFINES = -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__
  DEP_FILE = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/m0p/dl_interrupt.c.obj.d
  FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch
  INCLUDES = -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include
  OBJECT_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir
  OBJECT_FILE_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/m0p
  TARGET_COMPILE_PDB = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/ti_mspm0_driverlib.pdb
  TARGET_PDB = lib/libti_mspm0_driverlib.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target ti_mspm0_driverlib


#############################################
# Link the static library lib/libti_mspm0_driverlib.a

build lib/libti_mspm0_driverlib.a: C_STATIC_LIBRARY_LINKER__ti_mspm0_driverlib_Debug cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_common.c.obj cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_uart.c.obj cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_timer.c.obj cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_vref.c.obj cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/m0p/dl_interrupt.c.obj
  LANGUAGE_COMPILE_FLAGS = -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb
  OBJECT_DIR = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/ti_mspm0_driverlib.pdb
  TARGET_FILE = lib/libti_mspm0_driverlib.a
  TARGET_PDB = lib/libti_mspm0_driverlib.pdb


#############################################
# Utility command for edit_cache

build cmake/ti-mspm0/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/cmake/ti-mspm0 && /usr/local/bin/ccmake -S/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2 -B/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build cmake/ti-mspm0/edit_cache: phony cmake/ti-mspm0/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build cmake/ti-mspm0/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/cmake/ti-mspm0 && /usr/local/bin/cmake --regenerate-during-build -S/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2 -B/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build cmake/ti-mspm0/rebuild_cache: phony cmake/ti-mspm0/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libti_mspm0_driverlib.a: phony lib/libti_mspm0_driverlib.a

build mspm0_timer_test2: phony bin/mspm0_timer_test2.elf

build mspm0_timer_test2.elf: phony bin/mspm0_timer_test2.elf

build ti_mspm0_driverlib: phony lib/libti_mspm0_driverlib.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build

build all: phony bin/mspm0_timer_test2.elf cmake/ti-mspm0/all

# =============================================================================

#############################################
# Folder: /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build/cmake/ti-mspm0

build cmake/ti-mspm0/all: phony lib/libti_mspm0_driverlib.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/CMakeLists.txt /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/build-config.cmake /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/gcc-arm-none-eabi.cmake /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/ti-mspm0/CMakeLists.txt /usr/local/share/cmake-3.30/Modules/CMakeASMCompiler.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeASMInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeCCompilerABI.c /usr/local/share/cmake-3.30/Modules/CMakeCInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp /usr/local/share/cmake-3.30/Modules/CMakeCXXInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineASMCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake /usr/local/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake /usr/local/share/cmake-3.30/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake /usr/local/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/local/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake /usr/local/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake /usr/local/share/cmake-3.30/Modules/CMakeSystem.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestASMCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake /usr/local/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-ASM.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-C.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU.cmake /usr/local/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/local/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake /usr/local/share/cmake-3.30/Modules/Platform/Generic.cmake CMakeCache.txt CMakeFiles/3.30.3/CMakeASMCompiler.cmake CMakeFiles/3.30.3/CMakeCCompiler.cmake CMakeFiles/3.30.3/CMakeCXXCompiler.cmake CMakeFiles/3.30.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/CMakeLists.txt /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/build-config.cmake /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/gcc-arm-none-eabi.cmake /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/ti-mspm0/CMakeLists.txt /usr/local/share/cmake-3.30/Modules/CMakeASMCompiler.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeASMInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeCCompilerABI.c /usr/local/share/cmake-3.30/Modules/CMakeCInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp /usr/local/share/cmake-3.30/Modules/CMakeCXXInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineASMCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake /usr/local/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake /usr/local/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake /usr/local/share/cmake-3.30/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake /usr/local/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake /usr/local/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake /usr/local/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake /usr/local/share/cmake-3.30/Modules/CMakeSystem.cmake.in /usr/local/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestASMCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake /usr/local/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake /usr/local/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-ASM.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-C.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake /usr/local/share/cmake-3.30/Modules/Compiler/GNU.cmake /usr/local/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /usr/local/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake /usr/local/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake /usr/local/share/cmake-3.30/Modules/Platform/Generic.cmake CMakeCache.txt CMakeFiles/3.30.3/CMakeASMCompiler.cmake CMakeFiles/3.30.3/CMakeCCompiler.cmake CMakeFiles/3.30.3/CMakeCXXCompiler.cmake CMakeFiles/3.30.3/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
