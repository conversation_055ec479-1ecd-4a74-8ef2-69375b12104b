[{"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -o CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Src/main.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Src/main.c", "output": "CMakeFiles/mspm0_timer_test2.dir/code/User/Src/main.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -o CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Src/hardware_init.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Src/hardware_init.c", "output": "CMakeFiles/mspm0_timer_test2.dir/code/User/Src/hardware_init.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -o CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/startup_mspm0g3507.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/startup_mspm0g3507.c", "output": "CMakeFiles/mspm0_timer_test2.dir/cmake/startup_mspm0g3507.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -DUSE_FULL_ASSERT -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/User/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/App/Inc -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -o CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/syscalls.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/cmake/syscalls.c", "output": "CMakeFiles/mspm0_timer_test2.dir/cmake/syscalls.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch -o cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_common.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_common.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_common.c", "output": "cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_common.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch -o cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_uart.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_uart.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_uart.c", "output": "cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_uart.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch -o cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_timer.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_timer.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_timer.c", "output": "cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_timer.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch -o cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_vref.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_vref.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/dl_vref.c", "output": "cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/dl_vref.c.obj"}, {"directory": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/build", "command": "/usr/share/arm-gnu-toolchain-14.2.rel1-x86_64-arm-none-eabi/bin/arm-none-eabi-gcc -DASSERT_ENABLED -DDEBUG -DDEBUG_PRINT_ENABLED -DDeviceFamily_MSPM0G350X -D__MSPM0G3507__ -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/devices/msp/peripherals -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p -I/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/CMSIS/Core/Include -mcpu=cortex-m0plus -mthumb -Wall -Wextra -fdata-sections -ffunction-sections -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wall -Wextra -Wshadow -Wundef -Wno-unused-parameter -Og -g3 -ggdb -O0 -g3 -ggdb -std=gnu11 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable -Wno-pedantic -Wno-enum-int-mismatch -o cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/m0p/dl_interrupt.c.obj -c /home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p/dl_interrupt.c", "file": "/home/<USER>/elect/code/mspm0g3507/mspm0_timer_test2/code/Driver/ti/driverlib/m0p/dl_interrupt.c", "output": "cmake/ti-mspm0/CMakeFiles/ti_mspm0_driverlib.dir/__/__/code/Driver/ti/driverlib/m0p/dl_interrupt.c.obj"}]