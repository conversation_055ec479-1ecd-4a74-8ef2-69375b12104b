#!/bin/bash

# MSPM0G3507 定时器测试项目构建脚本
# 用于验证修复后的中断向量表配置

echo "=== MSPM0G3507 定时器测试项目构建 ==="
echo "修复内容："
echo "1. 添加设备头文件包含 (msp.h)"
echo "2. 修正中断向量表排列"
echo "3. 增强中断处理和调试功能"
echo ""

# 清理并重新构建
echo "清理构建目录..."
rm -rf build
mkdir build
cd build

echo "配置CMake..."
cmake ..

echo "开始构建..."
make

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 构建成功！"
    echo ""
    echo "=== 内存使用情况 ==="
    arm-none-eabi-size bin/mspm0_timer_test2.elf
    
    echo ""
    echo "=== 关键符号验证 ==="
    echo "TIMG0_IRQHandler地址："
    arm-none-eabi-nm bin/mspm0_timer_test2.elf | grep TIMG0_IRQHandler
    
    echo "Default_Handler地址："
    arm-none-eabi-nm bin/mspm0_timer_test2.elf | grep Default_Handler
    
    echo ""
    echo "=== 中断向量表验证 ==="
    echo "TIMG0中断向量（偏移0x80）："
    arm-none-eabi-objdump -s -j .isr_vector bin/mspm0_timer_test2.elf | grep " 0080"
    
    echo ""
    echo "🎯 调试建议："
    echo "1. 在Default_Handler设置断点，查看active_irq变量"
    echo "2. 验证uwTick变量每1ms递增"
    echo "3. 观察PA14引脚LED每1秒闪烁"
    echo "4. 确认不再进入Default_Handler"
    
else
    echo "❌ 构建失败！"
    exit 1
fi
